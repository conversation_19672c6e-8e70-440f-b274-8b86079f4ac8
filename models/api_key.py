from typing import Op<PERSON>
from datetime import datetime
from uuid import UUID
from pydantic import Field, field_validator
from .base import BaseModel

class APIKey(BaseModel):
    """
    Model for API keys with tenant context.
    An API key can be assigned to either a specific user or a single role, but not both.
    """
    key_hash: str = Field(..., description="Hashed value of the API key")
    name: Optional[str] = Field(None, description="Optional name/description for the API key")
    expires_at: Optional[datetime] = Field(None, description="Optional expiration date for the API key")
    last_used_at: Optional[datetime] = Field(None, description="Timestamp of last usage")
    is_active: bool = Field(True, description="Whether the API key is currently active")
    
    # Assignment can be to either a user or a single role
    user_id: Optional[UUID] = Field(None, description="UUID of the user this key is assigned to, if any")
    role_id: Optional[UUID] = Field(None, description="UUID of the role this key is assigned to, if any")
    
    @field_validator('user_id', 'role_id')
    @classmethod
    def validate_assignment(cls, v, info):
        """Ensure API key is assigned to either user or role, not both"""
        # Get the current field name and all field values
        field_name = info.field_name
        values = info.data

        user_id = values.get('user_id') if field_name != 'user_id' else v
        role_id = values.get('role_id') if field_name != 'role_id' else v

        if user_id and role_id:
            raise ValueError("API key cannot be assigned to both user and role")
        if not user_id and not role_id:
            raise ValueError("API key must be assigned to either user or role")

        return v
    
    # Note: Collection name and indexes are managed through models/registry.py
    # and database setup code in data_access/ modules