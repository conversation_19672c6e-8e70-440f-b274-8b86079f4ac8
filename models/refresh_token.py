"""
Refresh Token Model for secure token storage and management
"""

from datetime import datetime, timezone
from typing import Optional, Dict, Any
from uuid import UUID
from pydantic import Field, field_validator
from .base import BaseModel


class RefreshToken(BaseModel):
    """
    Model for storing refresh tokens in database with security features
    
    This model implements the refresh token pattern for secure JWT authentication:
    - Stores refresh tokens securely in database
    - Tracks token families for theft detection
    - Supports token rotation and revocation
    - Includes metadata for security auditing
    """
    
    # Core token fields
    token_id: str = Field(..., description="Unique token identifier (JTI from JWT)")
    user_id: UUID = Field(..., description="User this token belongs to")
    token_hash: str = Field(..., description="Hashed refresh token value")
    
    # Expiration and lifecycle
    expires_at: datetime = Field(..., description="Token expiration timestamp")
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    last_used_at: Optional[datetime] = Field(None, description="Last time token was used")
    
    # Security and revocation
    is_revoked: bool = Field(False, description="Whether token has been revoked")
    revoked_at: Optional[datetime] = Field(None, description="When token was revoked")
    revoked_reason: Optional[str] = Field(None, description="Reason for revocation")
    
    # Token family tracking for theft detection
    family_id: str = Field(..., description="Token family identifier for rotation tracking")
    parent_token_id: Optional[str] = Field(None, description="Previous token in family chain")
    
    # Metadata for security auditing
    client_info: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Client metadata")
    ip_address: Optional[str] = Field(None, description="IP address when token was created")
    user_agent: Optional[str] = Field(None, description="User agent when token was created")
    
    @field_validator('expires_at', mode='before')
    @classmethod
    def ensure_timezone_aware(cls, v):
        """Ensure expires_at is timezone aware"""
        if isinstance(v, datetime) and v.tzinfo is None:
            return v.replace(tzinfo=timezone.utc)
        return v

    @field_validator('created_at', mode='before')
    @classmethod
    def ensure_created_at_timezone_aware(cls, v):
        """Ensure created_at is timezone aware"""
        if isinstance(v, datetime) and v.tzinfo is None:
            return v.replace(tzinfo=timezone.utc)
        return v
    
    def is_expired(self) -> bool:
        """Check if token is expired"""
        return datetime.now(timezone.utc) >= self.expires_at
    
    def is_valid(self) -> bool:
        """Check if token is valid (not expired and not revoked)"""
        return not self.is_expired() and not self.is_revoked
    
    def revoke(self, reason: str = "Manual revocation") -> None:
        """Revoke the token"""
        self.is_revoked = True
        self.revoked_at = datetime.now(timezone.utc)
        self.revoked_reason = reason
    
    def update_last_used(self) -> None:
        """Update last used timestamp"""
        self.last_used_at = datetime.now(timezone.utc)
    
    # Note: Collection name and indexes are managed through models/registry.py
    # and database setup code in data_access/ modules


class TokenBlacklist(BaseModel):
    """
    Model for blacklisted JWT tokens (for logout scenarios)
    
    This model stores JTI (JWT ID) values of tokens that should be rejected
    even if they are cryptographically valid. Used for:
    - Immediate logout (blacklist current access token)
    - Security incidents (blacklist compromised tokens)
    - User account deactivation
    """
    
    jti: str = Field(..., description="JWT ID to blacklist")
    user_id: UUID = Field(..., description="User this token belonged to")
    token_type: str = Field(..., description="Type of token (access/refresh)")
    
    # Lifecycle fields
    blacklisted_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    expires_at: datetime = Field(..., description="When the original token would expire")
    
    # Metadata
    reason: str = Field(..., description="Reason for blacklisting")
    ip_address: Optional[str] = Field(None, description="IP address when blacklisted")
    
    def is_expired(self) -> bool:
        """Check if the blacklisted token would have expired naturally"""
        return datetime.now(timezone.utc) >= self.expires_at
    
    # Note: Collection name and indexes are managed through models/registry.py
    # and database setup code in data_access/ modules
