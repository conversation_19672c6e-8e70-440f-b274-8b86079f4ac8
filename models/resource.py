from typing import Any, Dict, Optional, List
from datetime import datetime, timezone
from enum import Enum
from uuid import UUID
from pydantic import Field, field_serializer, computed_field
from .base import BaseModelWithTenant, BaseModelWithTenantNoTracking
from config import settings

class ResourceStatus(str, Enum):
    PENDING = "pending"              # Initial state - resource created, ready for processing
    PROCESSING = "processing"        # Currently being processed (upload + indexing)
    PROCESSED = "processed"          # Successfully processed and indexed
    FAILED = "failed"               # Processing failed

    def __str__(self) -> str:
        return self.value

class Category(BaseModelWithTenant):
    name: str
    description: Optional[str] = None
    color: Optional[str] = None
    
class Tag(BaseModelWithTenant):
    """Flexible tagging system"""
    name: str
    color: Optional[str] = None
    usage_count: int = 0

class ResourceType(str, Enum):
    FILE = "file"
    ARTICLE = "article"
    WEB = "web"

    def __str__(self) -> str:
        return self.value

class Shelf(BaseModelWithTenant):
    """Shelf for organizing resources within a library - supports mixed resource types"""
    library_id: UUID
    name: str
    description: Optional[str] = None
    resource_count: int = 0
    # Removed resource_type restriction - shelves can now contain mixed resource types

    @field_serializer('library_id')
    def serialize_library_id(self, value: UUID | None) -> str | None:
        return str(value) if value is not None else None

class ResourceBase(BaseModelWithTenant):
    """Base model for all resources"""
    title: str
    description: Optional[str] = None
    status: ResourceStatus = ResourceStatus.PENDING
    owner_id: UUID
    task_id: Optional[UUID] = None
    shelf_ids: Optional[List[UUID]] = Field(default_factory=list, description="List of shelf IDs this resource belongs to")

    @field_serializer('owner_id', 'task_id')
    def serialize_uuids(self, value: UUID | None) -> str | None:
        return str(value) if value is not None else None

    @field_serializer('shelf_ids')
    def serialize_shelf_ids(self, value: List[UUID] | None) -> List[str] | None:
        return [str(shelf_id) for shelf_id in value] if value else []

class FileResource(ResourceBase):
    """Model for file-based resources"""
    filename: str 
    file_path: str = "" 
    file_type: Optional[str] = None
    size: int
    storage_key: Optional[str] = None 
    storage_provider: Optional[str] = None
    processing_strategy: Optional[str] = None
    
    def __init__(self, **data):
        super().__init__(**data)

    @computed_field
    def file_url(self) -> Optional[str]:
        if self.storage_key:
            # Generate URL based on the files endpoint with RESTful /api/ prefix
            # The storage_key should be used as-is without additional /files/ prefix
            return f"{settings.app_url}/api/files/{self.storage_key}"
        return None

class ArticleResource(ResourceBase):
    """Model for article resources"""
    content: str
    author: Optional[str] = None
    tags: List[str] = []
    published_date: Optional[datetime] = None

    def __init__(self, **data):
        super().__init__(**data)
    
    @field_serializer('published_date')
    def serialize_published_date(self, value: datetime | None) -> str | None:
        if value is None:
            return None
        if value.tzinfo is None:
            value = value.replace(tzinfo=timezone.utc)
        return value.isoformat().replace('+00:00', 'Z')

class WebResource(ResourceBase):
    """Model for web-crawled resources with simple parent-child relationships"""
    url: str
    content: str
    last_crawled: datetime
    crawl_depth: Optional[int] = None
    crawl_strategy: Optional[str] = None  # "single", "recursive", "sitemap"
    response_status: Optional[int] = None

    # Simple parent-child relationship
    parent_url: Optional[str] = None      # URL of the parent page

    def __init__(self, **data):
        super().__init__(**data)

    @field_serializer('last_crawled')
    def serialize_datetime_fields(self, value: datetime | None) -> str | None:
        if value is None:
            return None
        if value.tzinfo is None:
            value = value.replace(tzinfo=timezone.utc)
        return value.isoformat().replace('+00:00', 'Z')

class Chunk(BaseModelWithTenantNoTracking):
    """Model for text chunks from any resource type - optimized for Weaviate storage"""
    
    # Core content
    text: str = Field(..., description="The actual chunk text content")
    
    # Resource relationship
    resource_id: UUID = Field(..., description="Reference to parent resource")
    resource_type: str = Field(..., description="Type of parent resource (file/article/web)")
    shelf_ids: Optional[List[UUID]] = Field(default_factory=list, description="List of shelf IDs this chunk belongs to")
    chunk_index: int = Field(..., description="Sequential position in document (0-based)")

    # Optional metadata (resource-specific info)
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Flexible metadata storage")

    # Processing info
    created_at: Optional[datetime] = Field(None, description="When chunk was processed")
    created_by: UUID | None = None

    @field_serializer('resource_id')
    def serialize_resource_id(self, value: UUID) -> str:
        return str(value)

    @field_serializer('shelf_ids')
    def serialize_shelf_ids(self, value: List[UUID] | None) -> List[str] | None:
        return [str(shelf_id) for shelf_id in value] if value else []
    
    @field_serializer('created_at')
    def serialize_datetime(self, value: datetime | None) -> str | None:
        if value is None:
            return None
        if value.tzinfo is None:
            value = value.replace(tzinfo=timezone.utc)
        return value.isoformat().replace('+00:00', 'Z')
    
    def __init__(self, **data):
        super().__init__(**data)


# Metadata extraction guidelines for each resource type
# FileResource metadata (stored in chunk.metadata):
# {
#     "filename": "document.pdf",
#     "file_type": "pdf",
#     "page_number": 5,           # if applicable (PDF, Word)
# }

# ArticleResource metadata (stored in chunk.metadata):
# {
#     "title": "How to Setup Authentication",
#     "author": "John Doe",
#     "tags": ["auth", "security", "setup"],
# }

# WebResource metadata (stored in chunk.metadata):
# {
#     "url": "https://example.com/docs/auth",
#     "title": "Authentication Guide",
#     "crawl_depth": 2,
# }
