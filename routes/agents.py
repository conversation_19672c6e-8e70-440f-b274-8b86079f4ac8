import asyncio
from fastapi import APIRouter, HTTPException, Depends, Body, status, Query, Path
from graph import react_graph

from typing import Optional, List, Union, Any, Literal, Dict, Dict
import logging
from datetime import datetime
from enum import Enum

# Assistant UI related imports
from assistant_stream import create_run, Run<PERSON>ontroller
from assistant_stream.serialization import DataStreamResponse
from langchain_core.messages import (
    HumanMessage,
    AIMessageChunk,
    AIMessage,
    ToolMessage,
    SystemMessage,
    BaseMessage,
)

from middleware.permission import PermissionChecker
from middleware.rate_limiter import rate_limit
from models.role import Permission
from session.running_context import RunningContext
from security.unified_auth_middleware import get_current_user, create_running_context
from security.auth_middleware import StatelessUser, StatelessRunningContext
from models.agent import Agent, AgentIntegrationConfig, AgentActionConfig
from models.ai_model import AIModel
from services import agent_service
from services.integration_service import IntegrationService
from pydantic import BaseModel, Field

class ModelVendor(str, Enum):
    """Supported model vendors"""
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    GOOGLE = "google"
    OLLAMA = "ollama"

    def __str__(self) -> str:
        return self.value

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/agents", tags=["agents"])

# Define permission checkers for different operations
create_agent_permission = PermissionChecker([Permission.CREATE_AGENT])
read_agent_permission = PermissionChecker([Permission.READ_AGENT])
update_agent_permission = PermissionChecker([Permission.UPDATE_AGENT])
delete_agent_permission = PermissionChecker([Permission.DELETE_AGENT])
execute_agent_permission = PermissionChecker([Permission.EXECUTE_AGENT])

@router.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "ok", "service": "agents"}

@router.get("/test")
async def test_agents():
    """Test endpoint without authentication"""
    try:
        # This is a simple test - in production you'd want proper auth
        from models.user import User
        from models.agent import Agent, AIModel
        from uuid import uuid4
        
        # Create a dummy user for testing
        test_user = User(
            id=uuid4(),
            email="<EMAIL>",
            role_id=uuid4(),
            tenant_id=uuid4()
        )
        
        # Test if the agent service can list agents
        agents = await agent_service.list_agents(test_user)
        
        # Test creating a simple agent
        test_agent = Agent(
            name="Test Agent",
            description="A test agent for verification",
            model=AIModel(
                provider="openai",
                model_name="gpt-4",
                temperature=0.1
            ),
            owner_id=test_user.id,
            tenant_id=test_user.tenant_id
        )
        
        created_agent = await agent_service.create_agent(test_agent, test_user)
        
        # List agents again to see the new count
        agents_after = await agent_service.list_agents(test_user)
        
        return {
            "status": "ok", 
            "agents_count_before": len(agents),
            "agents_count_after": len(agents_after),
            "created_agent_id": str(created_agent.id) if created_agent else None,
            "message": "Agent service CRUD is working"
        }
    except Exception as e:
        import traceback
        return {
            "status": "error",
            "error": str(e),
            "traceback": traceback.format_exc(),
            "message": "Agent service failed"
        }

@router.post("", response_model=Agent, status_code=status.HTTP_201_CREATED)
@rate_limit(rate_limit=20, window=60)  # 20 creates per minute
async def create_agent(
    agent: Agent,
    context: RunningContext = Depends(create_agent_permission)
):
    """Create a new agent
    
    Args:
        agent: Agent configuration to create
        context: User access context with permissions
        
    Returns:
        The created agent
        
    Raises:
        HTTPException: If creation fails or validation error occurs
    """
    try:
        # Validate required fields
        if not agent.name:
            raise ValueError("Name is a required field")
            
        # Set metadata fields
        agent.created_at = datetime.utcnow()
        agent.updated_at = datetime.utcnow()
        agent.created_by = context.current_user.user_id
        
        created_agent = await agent_service.create_agent(
            agent=agent,
            creator=context.current_user
        )
        return created_agent
    except ValueError as e:
        logger.warning(f"Validation error creating agent: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error creating agent: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error occurred while creating agent"
        )

@router.get("", response_model=List[Agent])
@rate_limit(rate_limit=100, window=60)  # 100 reads per minute
async def get_agents(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    type: Optional[str] = Query(None, description="Filter by agent type"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    context: RunningContext = Depends(read_agent_permission)
):
    """Get all agents with optional filtering
    
    Args:
        skip: Number of records to skip
        limit: Maximum number of records to return
        type: Optional filter by agent type
        is_active: Optional filter by active status
        context: User access context with permissions
        
    Returns:
        List of agents matching the criteria
    """
    try:
        # Build query conditions
        query_conditions = {}
        if type:
            query_conditions["type"] = type
        if is_active is not None:
            query_conditions["is_active"] = is_active
            
        agents = await agent_service.list_agents(
            requester=context.current_user,
            skip=skip,
            limit=limit,
            query_conditions=query_conditions
        )
        return agents
    except Exception as e:
        logger.error(f"Error listing agents: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error occurred while listing agents"
        )

@router.get("/{agent_id}", response_model=Agent)
@rate_limit(rate_limit=100, window=60)
async def get_agent(
    agent_id: str,
    context: RunningContext = Depends(read_agent_permission)
):
    """Get a specific agent by ID
    
    Args:
        agent_id: ID of the agent to retrieve
        context: User access context with permissions
        
    Returns:
        The requested agent if found
        
    Raises:
        HTTPException: If agent not found or other error occurs
    """
    try:
        agent = await agent_service.get_agent(
            agent_id=agent_id,
            requester=context.current_user
        )
        if not agent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Agent not found"
            )
        return agent
    except ValueError as e:
        logger.warning(f"Invalid agent ID format: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error retrieving agent: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error occurred while retrieving agent"
        )

@router.put("/{agent_id}", response_model=Agent)
@rate_limit(rate_limit=50, window=60)  # 50 updates per minute
async def update_agent(
    agent_id: str,
    agent: Agent,
    context: RunningContext = Depends(update_agent_permission)
):
    """Update an existing agent
    
    Args:
        agent_id: ID of the agent to update
        agent: Updated agent configuration
        context: User access context with permissions
        
    Returns:
        The updated agent
        
    Raises:
        HTTPException: If update fails or validation error occurs
    """
    try:
        # Update metadata
        agent.updated_at = datetime.utcnow()
        agent.updated_by = context.current_user.user_id
        
        updated_agent = await agent_service.update_agent(
            agent_id=agent_id,
            agent=agent,
            updater=context.current_user
        )
        if not updated_agent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Agent not found"
            )
        return updated_agent
    except ValueError as e:
        logger.warning(f"Validation error updating agent: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error updating agent: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error occurred while updating agent"
        )

@router.delete("/{agent_id}", status_code=status.HTTP_204_NO_CONTENT)
@rate_limit(rate_limit=20, window=60)  # 20 deletes per minute
async def delete_agent(
    agent_id: str,
    context: RunningContext = Depends(delete_agent_permission)
):
    """Delete an agent
    
    Args:
        agent_id: ID of the agent to delete
        context: User access context with permissions
        
    Raises:
        HTTPException: If deletion fails or agent not found
    """
    try:
        success = await agent_service.delete_agent(
            agent_id=agent_id,
            deleter=context.current_user
        )
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Agent not found"
            )
    except ValueError as e:
        logger.warning(f"Invalid agent ID format: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error deleting agent: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error occurred while deleting agent"
        )

@router.post("/{agent_id}/execute")
@rate_limit(rate_limit=50, window=60)  # 50 executions per minute
async def execute_agent_query(
    agent_id: str,
    query: str = Body(..., min_length=1, max_length=1000),
    context: RunningContext = Depends(execute_agent_permission)
):
    """Execute a query using an agent
    
    Args:
        agent_id: ID of the agent to use
        query: The query to execute (between 1 and 1000 characters)
        context: User access context with permissions
        
    Returns:
        The execution results
        
    Raises:
        HTTPException: If execution fails or validation error occurs
    """
    try:
        agent = await agent_service.get_agent(
            agent_id=agent_id,
            requester=context.current_user
        )
        if not agent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Agent not found"
            )
            
        if not agent.is_active:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Agent is not active"
            )
        
        result = await agent_service.execute_query(
            agent=agent,
            query=query,
            executor=context.current_user
        )
        return result
    except ValueError as e:
        logger.warning(f"Validation error executing query: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error executing query: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error occurred while executing query"
        )

@router.get("/models/{vendor}")
@rate_limit(rate_limit=100, window=60)
async def get_available_models(
    vendor: str = Path(..., description="The model vendor (e.g., openai, google, anthropic)"),
    context: RunningContext = Depends(read_agent_permission)
):
    """Get available models for a vendor
    
    Args:
        vendor: The model vendor to get models for
        context: User access context with permissions
        
    Returns:
        List of available models for the vendor
        
    Raises:
        HTTPException: If vendor is invalid or error occurs
    """
    try:
        # Validate vendor
        if vendor not in [v.value for v in ModelVendor]:
            raise ValueError(f"Invalid vendor. Must be one of: {[v.value for v in ModelVendor]}")
            
        models = await agent_service.get_available_models(vendor)
        return models
    except ValueError as e:
        logger.warning(f"Invalid vendor specified: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error getting available models: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error occurred while getting available models"
        )

@router.post("/test-create")
async def test_create_agent():
    """Test endpoint for creating an agent without authentication"""
    try:
        from models.user import User
        from uuid import uuid4
        
        # Create a fake user for testing
        fake_tenant_id = uuid4()
        fake_user_id = uuid4()
        fake_user = User(
            id=fake_user_id,
            email="<EMAIL>",
            is_active=True,
            role_id=uuid4(),  # Fake role ID
            tenant_id=fake_tenant_id
        )
        
        # Create a test agent
        test_agent = Agent(
            id=uuid4(),
            name="Test Agent",
            description="A test agent for API validation",
            model=AIModel(
                provider="openai",
                model_name="gpt-4"
            ),
            owner_id=fake_user_id,
            tenant_id=fake_tenant_id,  # Use same tenant ID
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        print(f"Creating test agent with model: {test_agent.model}")
        
        # Use the service to create the agent
        created_agent = await agent_service.create_agent(
            agent=test_agent,
            creator=fake_user
        )
        
        return {"status": "success", "agent": created_agent}
        
    except Exception as e:
        print(f"Error in test create: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Test creation failed: {str(e)}"
        )

# Request models for agent integration management
class AddIntegrationRequest(BaseModel):
    integration_id: str  # UUID as string
    user_connection_id: Optional[str] = None  # UUID as string
    custom_settings: Optional[dict] = None

class ConfigureActionRequest(BaseModel):
    action_id: str  # UUID as string
    is_enabled: bool = True
    is_visible: bool = True
    custom_parameters: Optional[dict] = None
    display_name: Optional[str] = None
    display_order: Optional[int] = None

# Initialize integration service
integration_service = IntegrationService()

# Agent Integration Management Routes
@router.post("/{agent_id}/integrations")
async def add_integration_to_agent(
    agent_id: str,
    request: AddIntegrationRequest,
    context: RunningContext = Depends(update_agent_permission)
):
    """Add an integration to an agent"""
    try:
        from uuid import UUID
        agent_uuid = UUID(agent_id)
        integration_uuid = UUID(request.integration_id)
        connection_uuid = UUID(request.user_connection_id) if request.user_connection_id else None
        
        # Get the agent
        agent = await agent_service.get_agent(agent_uuid)
        if not agent:
            raise HTTPException(status_code=404, detail="Agent not found")
        
        # Verify ownership
        if agent.owner_id != context.user_id:
            raise HTTPException(status_code=403, detail="Access denied")
        
        # Add the integration
        agent.add_integration(
            integration_id=integration_uuid,
            user_connection_id=connection_uuid,
            custom_settings=request.custom_settings
        )
        
        # Save the agent
        await agent_service.update_agent(agent_uuid, agent)
        
        return {"message": "Integration added to agent successfully"}
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="Failed to add integration to agent")

@router.get("/{agent_id}/integrations")
async def get_agent_integrations(
    agent_id: str,
    context: RunningContext = Depends(read_agent_permission)
):
    """Get all integrations configured for an agent"""
    try:
        from uuid import UUID
        agent_uuid = UUID(agent_id)
        
        # Get the agent
        agent = await agent_service.get_agent(agent_uuid)
        if not agent:
            raise HTTPException(status_code=404, detail="Agent not found")
        
        # Verify ownership
        if agent.owner_id != context.user_id:
            raise HTTPException(status_code=403, detail="Access denied")
        
        # Get integration details
        integrations_with_details = []
        for config in agent.integration_configs:
            integration = await integration_service.get_integration(config.integration_id)
            if integration:
                integrations_with_details.append({
                    "integration": integration,
                    "config": config,
                    "actions": await integration_service.get_integration_actions(config.integration_id)
                })
        
        return integrations_with_details
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.delete("/{agent_id}/integrations/{integration_id}")
async def remove_integration_from_agent(
    agent_id: str,
    integration_id: str,
    context: RunningContext = Depends(update_agent_permission)
):
    """Remove an integration from an agent"""
    try:
        from uuid import UUID
        agent_uuid = UUID(agent_id)
        integration_uuid = UUID(integration_id)
        
        # Get the agent
        agent = await agent_service.get_agent(agent_uuid)
        if not agent:
            raise HTTPException(status_code=404, detail="Agent not found")
        
        # Verify ownership
        if agent.owner_id != context.user_id:
            raise HTTPException(status_code=403, detail="Access denied")
        
        # Remove the integration
        agent.remove_integration(integration_uuid)
        
        # Save the agent
        await agent_service.update_agent(agent_uuid, agent)
        
        return {"message": "Integration removed from agent successfully"}
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/{agent_id}/integrations/{integration_id}/actions")
async def configure_integration_action(
    agent_id: str,
    integration_id: str,
    request: ConfigureActionRequest,
    context: RunningContext = Depends(update_agent_permission)
):
    """Configure an action for an integration in an agent"""
    try:
        from uuid import UUID
        agent_uuid = UUID(agent_id)
        integration_uuid = UUID(integration_id)
        action_uuid = UUID(request.action_id)
        
        # Get the agent
        agent = await agent_service.get_agent(agent_uuid)
        if not agent:
            raise HTTPException(status_code=404, detail="Agent not found")
        
        # Verify ownership
        if agent.owner_id != context.user_id:
            raise HTTPException(status_code=403, detail="Access denied")
        
        # Configure the action
        if request.is_enabled:
            agent.enable_integration_action(
                integration_id=integration_uuid,
                action_id=action_uuid,
                visible=request.is_visible,
                **request.custom_parameters or {}
            )
        else:
            agent.disable_integration_action(
                integration_id=integration_uuid,
                action_id=action_uuid
            )
        
        # Update display properties if provided
        config = agent.get_integration_config(integration_uuid)
        if config:
            action_config = config.get_action_config(action_uuid)
            if action_config:
                if request.display_name:
                    action_config.display_name = request.display_name
                if request.display_order is not None:
                    action_config.display_order = request.display_order
        
        # Save the agent
        await agent_service.update_agent(agent_uuid, agent)
        
        return {"message": "Action configured successfully"}
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/{agent_id}/integrations/{integration_id}/actions/{action_id}/enable")
async def enable_integration_action(
    agent_id: str,
    integration_id: str,
    action_id: str,
    context: RunningContext = Depends(update_agent_permission)
):
    """Enable a specific action for an integration"""
    try:
        from uuid import UUID
        agent_uuid = UUID(agent_id)
        integration_uuid = UUID(integration_id)
        action_uuid = UUID(action_id)
        
        # Get the agent
        agent = await agent_service.get_agent(agent_uuid)
        if not agent:
            raise HTTPException(status_code=404, detail="Agent not found")
        
        # Verify ownership
        if agent.owner_id != context.user_id:
            raise HTTPException(status_code=403, detail="Access denied")
        
        # Enable the action
        success = agent.enable_integration_action(integration_uuid, action_uuid)
        if not success:
            raise HTTPException(status_code=400, detail="Failed to enable action")
        
        # Save the agent
        await agent_service.update_agent(agent_uuid, agent)
        
        return {"message": "Action enabled successfully"}
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/{agent_id}/integrations/{integration_id}/actions/{action_id}/disable")
async def disable_integration_action(
    agent_id: str,
    integration_id: str,
    action_id: str,
    context: RunningContext = Depends(update_agent_permission)
):
    """Disable a specific action for an integration"""
    try:
        from uuid import UUID
        agent_uuid = UUID(agent_id)
        integration_uuid = UUID(integration_id)
        action_uuid = UUID(action_id)
        
        # Get the agent
        agent = await agent_service.get_agent(agent_uuid)
        if not agent:
            raise HTTPException(status_code=404, detail="Agent not found")
        
        # Verify ownership
        if agent.owner_id != context.user_id:
            raise HTTPException(status_code=403, detail="Access denied")
        
        # Disable the action
        success = agent.disable_integration_action(integration_uuid, action_uuid)
        if not success:
            raise HTTPException(status_code=400, detail="Failed to disable action")
        
        # Save the agent
        await agent_service.update_agent(agent_uuid, agent)
        
        return {"message": "Action disabled successfully"}
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/{agent_id}/integrations/{integration_id}/actions/{action_id}/hide")
async def hide_integration_action(
    agent_id: str,
    integration_id: str,
    action_id: str,
    context: RunningContext = Depends(update_agent_permission)
):
    """Hide a specific action for an integration (keep enabled but not visible)"""
    try:
        from uuid import UUID
        agent_uuid = UUID(agent_id)
        integration_uuid = UUID(integration_id)
        action_uuid = UUID(action_id)
        
        # Get the agent
        agent = await agent_service.get_agent(agent_uuid)
        if not agent:
            raise HTTPException(status_code=404, detail="Agent not found")
        
        # Verify ownership
        if agent.owner_id != context.user_id:
            raise HTTPException(status_code=403, detail="Access denied")
        
        # Hide the action
        success = agent.hide_integration_action(integration_uuid, action_uuid)
        if not success:
            raise HTTPException(status_code=400, detail="Failed to hide action")
        
        # Save the agent
        await agent_service.update_agent(agent_uuid, agent)
        
        return {"message": "Action hidden successfully"}
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/{agent_id}/actions/enabled")
async def get_enabled_actions(
    agent_id: str,
    integration_id: Optional[str] = Query(None),
    context: RunningContext = Depends(read_agent_permission)
):
    """Get all enabled actions for an agent, optionally filtered by integration"""
    try:
        from uuid import UUID
        agent_uuid = UUID(agent_id)
        integration_uuid = UUID(integration_id) if integration_id else None
        
        # Get the agent
        agent = await agent_service.get_agent(agent_uuid)
        if not agent:
            raise HTTPException(status_code=404, detail="Agent not found")
        
        # Verify ownership
        if agent.owner_id != context.user_id:
            raise HTTPException(status_code=403, detail="Access denied")
        
        # Get enabled actions
        enabled_action_ids = agent.get_enabled_actions(integration_uuid)
        
        # Get action details
        actions_with_details = []
        for action_id in enabled_action_ids:
            action = await agent_service.action_service.get_action_by_id(action_id)
            if action:
                actions_with_details.append(action)
        
        return actions_with_details
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

# Assistant UI Message Models for Chat Endpoint
class LanguageModelTextPart(BaseModel):
    type: Literal["text"]
    text: str
    providerMetadata: Optional[Any] = None

class LanguageModelImagePart(BaseModel):
    type: Literal["image"]
    image: str  # Will handle URL or base64 string
    mimeType: Optional[str] = None
    providerMetadata: Optional[Any] = None

class LanguageModelToolCallPart(BaseModel):
    type: Literal["tool-call"]
    toolCallId: str
    toolName: str
    args: Any
    providerMetadata: Optional[Any] = None

class LanguageModelToolResultContentPart(BaseModel):
    type: Literal["text", "image"]
    text: Optional[str] = None
    data: Optional[str] = None
    mimeType: Optional[str] = None

class LanguageModelToolResultPart(BaseModel):
    type: Literal["tool-result"]
    toolCallId: str
    toolName: str
    result: Any
    isError: Optional[bool] = None
    content: Optional[List[LanguageModelToolResultContentPart]] = None
    providerMetadata: Optional[Any] = None

class LanguageModelSystemMessage(BaseModel):
    role: Literal["system"]
    content: str

class LanguageModelUserMessage(BaseModel):
    role: Literal["user"]
    content: List[Union[LanguageModelTextPart, LanguageModelImagePart]]

class LanguageModelAssistantMessage(BaseModel):
    role: Literal["assistant"]
    content: List[Union[LanguageModelTextPart, LanguageModelToolCallPart]]

class LanguageModelToolMessage(BaseModel):
    role: Literal["tool"]
    content: List[LanguageModelToolResultPart]

LanguageModelV1Message = Union[
    LanguageModelSystemMessage,
    LanguageModelUserMessage,
    LanguageModelAssistantMessage,
    LanguageModelToolMessage,
]

class FrontendToolCall(BaseModel):
    name: str
    description: Optional[str] = None
    parameters: dict[str, Any]

class ChatRequest(BaseModel):
    system: Optional[str] = ""
    tools: Optional[List[FrontendToolCall]] = []
    messages: List[LanguageModelV1Message]

# Function to convert assistant-ui messages to langchain messages
def convert_to_langchain_messages(
    messages: List[LanguageModelV1Message],
) -> List[BaseMessage]:
    result = []

    for msg in messages:
        if msg.role == "system":
            result.append(SystemMessage(content=msg.content))

        elif msg.role == "user":
            content = []
            for p in msg.content:
                if isinstance(p, LanguageModelTextPart):
                    content.append({"type": "text", "text": p.text})
                elif isinstance(p, LanguageModelImagePart):
                    content.append({"type": "image_url", "image_url": p.image})
            # If only text content, use simple string
            if len(content) == 1 and content[0]["type"] == "text":
                result.append(HumanMessage(content=content[0]["text"]))
            else:
                result.append(HumanMessage(content=content))

        elif msg.role == "assistant":
            # Handle both text and tool calls
            text_parts = [
                p for p in msg.content if isinstance(p, LanguageModelTextPart)
            ]
            text_content = " ".join(p.text for p in text_parts)
            tool_calls = [
                {
                    "id": p.toolCallId,
                    "name": p.toolName,
                    "args": p.args,
                }
                for p in msg.content
                if isinstance(p, LanguageModelToolCallPart)
            ]
            result.append(AIMessage(content=text_content, tool_calls=tool_calls))

        elif msg.role == "tool":
            for tool_result in msg.content:
                result.append(
                    ToolMessage(
                        content=str(tool_result.result),
                        tool_call_id=tool_result.toolCallId,
                    )
                )

    return result

@router.post("/{agent_id}/chat")
@rate_limit(rate_limit=50, window=60)  # 50 chat requests per minute
async def chat_with_agent(
    agent_id: str,
    request: ChatRequest,
    context: RunningContext = Depends(execute_agent_permission)
):
    """Chat with an agent using streaming responses
    
    Args:
        agent_id: ID of the agent to chat with
        request: Chat request with messages and system prompt
        context: User access context with permissions
        
    Returns:
        Streaming chat response
        
    Raises:
        HTTPException: If agent not found or other error occurs
    """
    try:
        from uuid import UUID
        
        # Get the agent
        agent = await agent_service.get_agent(
            agent_id=agent_id,
            requester=context.current_user
        )
        if not agent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Agent not found"
            )
            
        if not agent.is_active:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Agent is not active"
            )
        
        # Convert assistant-ui messages to langchain format
        inputs = convert_to_langchain_messages(request.messages)
        
        # Create streaming response function
        async def run_chat(controller: RunController):
            try:
                # Extract the last user message as query
                user_messages = [msg for msg in inputs if isinstance(msg, HumanMessage)]
                if not user_messages:
                    controller.append_text("No user message found.")
                    return
                
                query = user_messages[-1].content
                if isinstance(query, list):
                    # Extract text from complex content
                    text_parts = [part.get("text", "") for part in query if isinstance(part, dict) and part.get("type") == "text"]
                    query = " ".join(text_parts)
                
                # Add system message if provided
                system_prompt = request.system or ""
                if system_prompt and agent.system_prompt:
                    combined_prompt = f"{agent.system_prompt}\n\n{system_prompt}"
                elif system_prompt:
                    combined_prompt = system_prompt
                else:
                    combined_prompt = agent.system_prompt or ""
                
                # Execute the query with the agent
                result = await agent_service.execute_query(
                    agent=agent,
                    query=str(query),
                    executor=context.current_user
                )
                
                # Stream the response
                if isinstance(result, dict):
                    response_text = result.get("response", str(result))
                else:
                    response_text = str(result)
                
                # Stream the text character by character for better UX
                for char in response_text:
                    controller.append_text(char)
                    
            except Exception as e:
                logger.error(f"Error in chat streaming: {str(e)}")
                controller.append_text(f"Error: {str(e)}")
        
        return DataStreamResponse(create_run(run_chat))
        
    except ValueError as e:
        logger.warning(f"Validation error in chat: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error in chat endpoint: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error occurred while processing chat"
        )

@router.post("/{agent_id}/simple-chat")
@rate_limit(rate_limit=50, window=60)  # 50 chat requests per minute
async def simple_chat_with_agent(
    agent_id: str,
    message: str = Body(..., min_length=1, max_length=2000),
    system_prompt: Optional[str] = Body(None),
    context: RunningContext = Depends(execute_agent_permission)
):
    """Simple chat with an agent (non-streaming)
    
    Args:
        agent_id: ID of the agent to chat with
        message: The message to send to the agent
        system_prompt: Optional system prompt override
        context: User access context with permissions
        
    Returns:
        The agent's response
        
    Raises:
        HTTPException: If agent not found or other error occurs
    """
    try:
        agent = await agent_service.get_agent(
            agent_id=agent_id,
            requester=context.current_user
        )
        if not agent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Agent not found"
            )
            
        if not agent.is_active:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Agent is not active"
            )
        
        # Use the existing execute_query method
        result = await agent_service.execute_query(
            agent=agent,
            query=message,
            executor=context.current_user
        )
        
        return {
            "agent_id": agent_id,
            "message": message,
            "response": result,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except ValueError as e:
        logger.warning(f"Validation error in simple chat: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error in simple chat endpoint: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error occurred while processing chat"
        )

@router.post("/chat")
@rate_limit(rate_limit=50, window=60)  # 50 chat requests per minute  
async def general_chat(
    request: ChatRequest,
    context: RunningContext = Depends(read_agent_permission)
):
    """Chat with an agent using streaming responses
    
    Args:
        request: Chat request with messages and system prompt
        context: User access context with permissions
        
    Returns:
        Streaming chat response
        
    Raises:
        HTTPException: If agent not found or other error occurs
    """
    try:
        # Convert assistant-ui messages to langchain format
        inputs = convert_to_langchain_messages(request.messages)
        
        # Create streaming response function
        async def run_general_chat(controller: RunController):
            try:
                # Extract the last user message
                user_messages = [msg for msg in inputs if isinstance(msg, HumanMessage)]
                if not user_messages:
                    controller.append_text("Hello! How can I help you today?")
                    return
                
                query = user_messages[-1].content
                if isinstance(query, list):
                    # Extract text from complex content
                    text_parts = [part.get("text", "") for part in query if isinstance(part, dict) and part.get("type") == "text"]
                    query = " ".join(text_parts)
                
                # For now, provide a simple echo response with basic AI-like behavior
                # In a real implementation, you'd want to use a default agent or model
                system_prompt = request.system or "You are a helpful AI assistant."
                
                # Simple response logic (replace with actual AI model call)
                response = f"I received your message: '{query}'. "
                if "hello" in query.lower() or "hi" in query.lower():
                    response += "Hello! How can I assist you today?"
                elif "help" in query.lower():
                    response += "I'm here to help! What do you need assistance with?"
                elif "test" in query.lower():
                    response += "Test successful! The chat endpoint is working correctly."
                else:
                    response += "I understand your message. This is a demo response from the general chat endpoint."
                
                # Stream the response character by character
                for char in response:
                    controller.append_text(char)
                    
            except Exception as e:
                logger.error(f"Error in general chat streaming: {str(e)}")
                controller.append_text(f"Sorry, I encountered an error: {str(e)}")
        
        return DataStreamResponse(create_run(run_general_chat))
        
    except Exception as e:
        logger.error(f"Error in general chat endpoint: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error occurred while processing chat"
        )

@router.post("/test-chat")
async def test_general_chat(request: ChatRequest):
    """Test chat endpoint without authentication for testing purposes
    
    Args:
        request: Chat request with messages and system prompt
        
    Returns:
        Streaming chat response
    """
    try:
        # Convert assistant-ui messages to langchain format
        inputs = convert_to_langchain_messages(request.messages)
        
        # Create streaming response function
        async def run_test_chat(controller: RunController):
            try:
                # Extract the last user message
                user_messages = [msg for msg in inputs if isinstance(msg, HumanMessage)]
                if not user_messages:
                    controller.append_text("Hello! How can I help you today? (This is a test endpoint)")
                    return
                
                query = user_messages[-1].content
                if isinstance(query, list):
                    # Extract text from complex content
                    text_parts = [part.get("text", "") for part in query if isinstance(part, dict) and part.get("type") == "text"]
                    query = " ".join(text_parts)
                
                # Test response
                system_prompt = request.system or "You are a helpful AI assistant in test mode."
                
                # Test response with markdown
                system_prompt = request.system or "You are a helpful AI assistant in test mode."
                
                # Response with markdown formatting for testing
                if "hello" in query.lower() or "hi" in query.lower():
                    response = """# Hello! 👋

This is the **test chat endpoint** working correctly!

## Features being tested:
- *Italic text*
- **Bold text** 
- `Code snippets`
- Lists and formatting

### Sample code block:
```python
def test_function():
    print("Hello from markdown!")
```

> This is a blockquote to test markdown rendering.

Everything is working! 🚀"""
                elif "help" in query.lower():
                    response = """## Help Section 📚

I'm the **test chat assistant**! Here's what I can do:

### Commands you can try:
1. Say `hello` - Get a formatted greeting
2. Ask about `markdown` - See formatting examples  
3. Request `code` - Get code examples
4. Say `test` - Basic functionality test

*Note: This is a test endpoint for verifying frontend-backend communication.*"""
                elif "markdown" in query.lower():
                    response = """# Markdown Test 📝

Here are various **markdown elements** working:

## Headers
### Sub-headers
#### And smaller ones

## Text Formatting
- *Italic text*
- **Bold text**
- ***Bold and italic***
- ~~Strikethrough~~
- `inline code`

## Code Blocks
```javascript
console.log("Hello from JavaScript!");
```

```python
def hello_world():
    return "Hello, World! 🌍"
```

## Lists
1. First item
2. Second item
   - Nested bullet
   - Another nested item
3. Third item

## Links and More
- [Assistant UI Docs](https://assistant-ui.com)
- Emojis: 🚀 ⭐ 💡 🎉

> **Quote**: "Markdown makes text formatting easy and readable!"

## Tables
| Feature | Status |
|---------|--------|
| Headers | ✅ |
| Lists | ✅ |
| Code | ✅ |
| Links | ✅ |

All markdown features should render properly! 🎯"""
                elif "code" in query.lower():
                    response = """# Code Examples 💻

Here are some **code snippets** in different languages:

## Python
```python
def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

# Generate first 10 Fibonacci numbers
for i in range(10):
    print(f"F({i}) = {fibonacci(i)}")
```

## JavaScript
```javascript
// React component example
const MyComponent = ({ name }) => {
  const [count, setCount] = useState(0);
  
  return (
    <div>
      <h1>Hello, {name}!</h1>
      <button onClick={() => setCount(count + 1)}>
        Count: {count}
      </button>
    </div>
  );
};
```

## Bash
```bash
#!/bin/bash
echo "Starting deployment..."
npm run build
docker build -t my-app .
docker run -p 3000:3000 my-app
echo "Deployment complete! 🚀"
```

All syntax highlighting should work! ✨"""
                elif "test" in query.lower():
                    response = """🧪 **Test successful!** 

The frontend can now communicate with the backend general chat endpoint.

### Connection Status: ✅ 
- Frontend → Backend: **Connected**
- Streaming: **Working**
- Markdown: **Rendering**

*Everything is functioning properly!*"""
                else:
                    response = f"""## Message Received 📨

I received your message: **"{query}"**

### Test Response
This is a *test response* from the backend general chat endpoint. 

The **playground is working!** 🎮

Try asking about:
- `hello` - Formatted greeting
- `markdown` - Formatting examples
- `code` - Code snippets  
- `help` - Available commands

> The assistant-ui markdown renderer should display this beautifully!"""
                
                # Stream the response character by character for better UX
                for char in response:
                    controller.append_text(char)
                    
            except Exception as e:
                logger.error(f"Error in test chat streaming: {str(e)}")
                controller.append_text(f"❌ Test Error: {str(e)}")
        
        return DataStreamResponse(create_run(run_test_chat))
        
    except Exception as e:
        logger.error(f"Error in test chat endpoint: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error occurred while processing test chat"
        )

# Test Graph Chat endpoint using react_playground_graph
@router.post("/test-graph-chat")
async def test_graph_chat(
    request: ChatRequest,
    current_user: StatelessUser = Depends(get_current_user),
    context: StatelessRunningContext = Depends(create_running_context)
):
    inputs = convert_to_langchain_messages(request.messages)

    async def run(controller: RunController):
        tool_calls = {}
        tool_calls_by_idx = {}

        # Get tenant-aware playground graph
        tenant_id = str(context.tenant_id) if context.tenant_id else "00000000-0000-0000-0000-000000000001"
        playground_graph = react_graph.get_playground_graph_for_tenant(tenant_id)

        async for msg, metadata in playground_graph.astream(
            {"messages": inputs},
            {
                "configurable": {
                    "system": request.system,
                    "frontend_tools": request.tools,
                }
            },
            stream_mode="messages",
        ):
            if isinstance(msg, ToolMessage):
                tool_controller = tool_calls[msg.tool_call_id]
                tool_controller.set_result(msg.content)

            if isinstance(msg, AIMessageChunk) or isinstance(msg, AIMessage):
                if msg.content:
                    controller.append_text(msg.content)

                for chunk in msg.tool_call_chunks:
                    if not chunk["index"] in tool_calls_by_idx:
                        tool_controller = await controller.add_tool_call(
                            chunk["name"], chunk["id"]
                        )
                        tool_calls_by_idx[chunk["index"]] = tool_controller
                        tool_calls[chunk["id"]] = tool_controller
                    else:
                        tool_controller = tool_calls_by_idx[chunk["index"]]

                    tool_controller.append_args_text(chunk["args"])

    return DataStreamResponse(create_run(run))

# Alternative simpler approach - use StreamingResponse instead of DataStreamResponse

from fastapi.responses import StreamingResponse
import json

# @router.post("/test-graph-chat")
# async def test_graph_chat(request: ChatRequest):
#     """Test graph chat endpoint using react_playground_graph for LangGraph/Assistant UI (SSE-style streaming)."""
#     try:
#         # Convert assistant-ui messages to langchain format
#         inputs = convert_to_langchain_messages(request.messages)

#         # Prepare the message list for the graph
#         messages = []
#         for msg in inputs:
#             messages.append(msg)

#         async def event_stream():
#             try:
#                 # Use astream for true streaming if available
#                 async for chunk in react_graph.react_playground_graph.astream({"messages": messages}):
#                     def to_dict_safe(msg):
#                         if hasattr(msg, "dict"):
#                             return msg.dict()
#                         elif hasattr(msg, "as_dict"):
#                             return msg.as_dict()
#                         elif hasattr(msg, "__dict__"):
#                             return dict(msg.__dict__)
#                         return msg
#                     if isinstance(chunk, list):
#                         data = [to_dict_safe(m) for m in chunk]
#                     else:
#                         data = to_dict_safe(chunk)
#                     # Send as SSE-style event for Assistant UI
#                     yield f"data: {json.dumps({'event': 'messages', 'data': data})}\n\n"
#             except Exception as stream_error:
#                 logger.warning(f"Streaming failed, falling back to ainvoke: {stream_error}")
#                 try:
#                     response = await react_graph.react_playground_graph.ainvoke({"messages": messages})
#                     def to_dict_safe(msg):
#                         if hasattr(msg, "dict"):
#                             return msg.dict()
#                         elif hasattr(msg, "as_dict"):
#                             return msg.as_dict()
#                         elif hasattr(msg, "__dict__"):
#                             return dict(msg.__dict__)
#                         return msg
#                     if isinstance(response, list):
#                         data = [to_dict_safe(m) for m in response]
#                     else:
#                         data = to_dict_safe(response)
#                     yield f"data: {json.dumps({'event': 'messages', 'data': data})}\n\n"
#                 except Exception as e:
#                     error_msg = {"event": "error", "data": str(e)}
#                     yield f"data: {json.dumps(error_msg)}\n\n"

#         return StreamingResponse(event_stream(), media_type="text/event-stream")
#     except Exception as e:
#         logger.error(f"Error in test graph chat endpoint: {str(e)}")
#         raise HTTPException(
#             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
#             detail="Internal server error occurred while processing test graph chat"
#         )

class PlaygroundMessage(BaseModel):
    """Message model for playground conversations."""
    role: str = Field(..., description="The role of the message sender")
    content: str = Field(..., description="The content of the message")

class PlaygroundChatRequest(BaseModel):
    """Request model for playground chat."""
    messages: List[PlaygroundMessage] = Field(..., description="List of messages in the conversation")
    agent_id: Optional[str] = Field(None, description="Optional agent ID to use for the conversation")
    stream: bool = Field(True, description="Whether to stream the response")

class PlaygroundChatResponse(BaseModel):
    """Response model for playground chat."""
    message: str = Field(..., description="The assistant's response")
    agent_used: Optional[Dict[str, Any]] = Field(None, description="Information about the agent used")
    execution_steps: Optional[List[str]] = Field(None, description="Steps executed during the conversation")

def convert_playground_messages_to_langchain(messages: List[PlaygroundMessage]):
    """Convert playground messages to LangChain message format."""
    langchain_messages = []
    for msg in messages:
        if msg.role == "user":
            langchain_messages.append(HumanMessage(content=msg.content))
        elif msg.role == "assistant":
            langchain_messages.append(AIMessage(content=msg.content))
        elif msg.role == "system":
            langchain_messages.append(SystemMessage(content=msg.content))
    return langchain_messages

@router.post("/{agent_id}/playground/chat")
@rate_limit(rate_limit=30, window=60)  # 30 playground chats per minute
async def playground_chat_with_agent(
    agent_id: str,
    request: PlaygroundChatRequest,
    context: RunningContext = Depends(execute_agent_permission)
):
    """
    Playground chat with a specific agent for testing and experimentation.
    
    This endpoint provides an interactive playground environment for testing agents
    with conversational context and streaming responses.
    """
    try:
        # Get the agent
        agent = await agent_service.get_agent(
            agent_id=agent_id,
            requester=context.current_user
        )
        if not agent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Agent not found"
            )
            
        if not agent.is_active:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Agent is not active"
            )
        
        # Convert playground messages to LangChain format
        langchain_messages = convert_playground_messages_to_langchain(request.messages)
        
        if request.stream:
            # Create streaming response function
            async def run_playground_chat(controller: RunController):
                try:
                    # Extract the last user message
                    user_messages = [msg for msg in langchain_messages if isinstance(msg, HumanMessage)]
                    if not user_messages:
                        controller.append_text("No user message found.")
                        return
                    
                    query = user_messages[-1].content
                    if isinstance(query, list):
                        # Extract text from complex content
                        text_parts = [part.get("text", "") for part in query if isinstance(part, dict) and part.get("type") == "text"]
                        query = " ".join(text_parts)
                    
                    # Show playground information
                    controller.append_text(f"🎮 Playground Mode - Agent: {agent.name}\n")
                    controller.append_text(f"📝 Processing: {query}\n\n")
                    
                    # Execute the query with the agent
                    result = await agent_service.execute_query(
                        agent=agent,
                        query=str(query),
                        executor=context.current_user
                    )
                    
                    # Stream the response
                    if isinstance(result, dict):
                        response_text = result.get("response", str(result))
                    else:
                        response_text = str(result)
                    
                    # Stream the text character by character for better UX
                    controller.append_text("🤖 Response:\n")
                    for char in response_text:
                        controller.append_text(char)
                    
                    # Add playground footer
                    controller.append_text(f"\n\n✅ Playground execution completed for agent: {agent.name}")
                        
                except Exception as e:
                    logger.error(f"Error in playground chat streaming: {str(e)}")
                    controller.append_text(f"❌ Playground Error: {str(e)}")
            
            return DataStreamResponse(create_run(run_playground_chat))
        
        else:
            # Non-streaming response
            user_messages = [msg for msg in langchain_messages if isinstance(msg, HumanMessage)]
            if not user_messages:
                return PlaygroundChatResponse(
                    message="No user message found.",
                    agent_used={"id": agent_id, "name": agent.name},
                    execution_steps=[]
                )
            
            query = user_messages[-1].content
            if isinstance(query, list):
                text_parts = [part.get("text", "") for part in query if isinstance(part, dict) and part.get("type") == "text"]
                query = " ".join(text_parts)
            
            # Execute the query
            result = await agent_service.execute_query(
                agent=agent,
                query=str(query),
                executor=context.current_user
            )
            
            response_text = result.get("response", str(result)) if isinstance(result, dict) else str(result)
            
            return PlaygroundChatResponse(
                message=response_text,
                agent_used={
                    "id": agent_id,
                    "name": agent.name,
                    "description": agent.description
                },
                execution_steps=["Query processed", "Agent executed", "Response generated"]
            )
    
    except ValueError as e:
        logger.warning(f"Validation error in playground chat: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error in playground chat endpoint: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error occurred while processing playground chat"
        )

@router.post("/playground/chat")
@rate_limit(rate_limit=20, window=60)  # 20 general playground chats per minute
async def general_playground_chat(
    request: PlaygroundChatRequest,
    context: RunningContext = Depends(read_agent_permission)
):
    """
    General playground chat that can route to appropriate agents or respond directly.
    
    This endpoint analyzes the user's query and automatically determines whether
    to execute it with an agent or respond directly.
    """
    try:
        # Convert playground messages to LangChain format
        langchain_messages = convert_playground_messages_to_langchain(request.messages)
        
        if request.stream:
            # Create streaming response function
            async def run_general_playground_chat(controller: RunController):
                try:
                    # Extract the last user message
                    user_messages = [msg for msg in langchain_messages if isinstance(msg, HumanMessage)]
                    if not user_messages:
                        controller.append_text("🎮 Welcome to Agent Playground! How can I help you today?")
                        return
                    
                    query = user_messages[-1].content
                    if isinstance(query, list):
                        text_parts = [part.get("text", "") for part in query if isinstance(part, dict) and part.get("type") == "text"]
                        query = " ".join(text_parts)
                    
                    controller.append_text("🎮 Playground Mode - Analyzing your request...\n")
                    
                    # If agent_id is provided, use that specific agent
                    if request.agent_id:
                        try:
                            agent = await agent_service.get_agent(
                                agent_id=request.agent_id,
                                requester=context.current_user
                            )
                            if agent and agent.is_active:
                                controller.append_text(f"🤖 Using Agent: {agent.name}\n\n")
                                
                                # Execute with the specified agent
                                result = await agent_service.execute_query(
                                    agent=agent,
                                    query=str(query),
                                    executor=context.current_user
                                )
                                
                                response_text = result.get("response", str(result)) if isinstance(result, dict) else str(result)
                                
                                for char in response_text:
                                    controller.append_text(char)
                                
                                controller.append_text(f"\n\n✅ Completed using agent: {agent.name}")
                                return
                        except Exception as e:
                            controller.append_text(f"⚠️ Error with specified agent: {str(e)}\n")
                    
                    # Fallback to general response
                    controller.append_text("💬 General Response Mode\n\n")
                    
                    # Simple response logic for demo
                    if "hello" in query.lower() or "hi" in query.lower():
                        response = "Hello! Welcome to the Agent Playground. This is where you can test and experiment with different agents."
                    elif "help" in query.lower():
                        response = "I can help you test agents in the playground! You can:\n- Chat with specific agents\n- Test agent responses\n- Experiment with different queries\n- See how agents process information"
                    elif "test" in query.lower():
                        response = "Test successful! The playground is working correctly. You can now experiment with agents and see their responses in real-time."
                    elif "agent" in query.lower():
                        response = "You're asking about agents! In this playground, you can test different agents by specifying an agent_id or let me route your query to the most appropriate agent."
                    else:
                        response = f"I understand your query: '{query}'\n\nThis is the general playground mode. For more advanced interactions, try specifying an agent_id or use the agent-specific playground endpoints."
                    
                    # Stream the response
                    for char in response:
                        controller.append_text(char)
                    
                    controller.append_text("\n\n🎮 Playground session completed!")
                        
                except Exception as e:
                    logger.error(f"Error in general playground chat: {str(e)}")
                    controller.append_text(f"❌ Playground Error: {str(e)}")
            
            return DataStreamResponse(create_run(run_general_playground_chat))
        
        else:
            # Non-streaming response
            user_messages = [msg for msg in langchain_messages if isinstance(msg, HumanMessage)]
            if not user_messages:
                return PlaygroundChatResponse(
                    message="Welcome to Agent Playground! How can I help you today?",
                    agent_used=None,
                    execution_steps=[]
                )
            
            query = user_messages[-1].content
            if isinstance(query, list):
                text_parts = [part.get("text", "") for part in query if isinstance(part, dict) and part.get("type") == "text"]
                query = " ".join(text_parts)
            
            # Simple response for non-streaming mode
            response = f"Playground processed your query: '{query}'. This is a demo response."
            
            return PlaygroundChatResponse(
                message=response,
                agent_used=None,
                execution_steps=["Query analyzed", "Response generated"]
            )
    
    except Exception as e:
        logger.error(f"Error in general playground chat endpoint: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error occurred while processing playground chat"
        )

@router.get("/playground/available")
@rate_limit(rate_limit=100, window=60)
async def get_playground_agents(
    context: RunningContext = Depends(read_agent_permission),
    limit: int = Query(50, description="Maximum number of agents to return"),
    skip: int = Query(0, description="Number of agents to skip")
):
    """
    Get available agents for playground testing.
    
    Returns a list of active agents that can be used in the playground environment.
    """
    try:
        # Get all active agents for the user
        agents = await agent_service.list_agents(
            requester=context.current_user,
            skip=skip,
            limit=limit,
            query_conditions={"is_active": True}
        )
        
        # Format agents for playground use
        playground_agents = []
        for agent in agents:
            playground_agents.append({
                "id": str(agent.id),
                "name": agent.name,
                "description": agent.description,
                "type": getattr(agent, 'type', 'general'),
                "status": "active",
                "created_at": agent.created_at,
                "model": getattr(agent, 'model', None),
                "playground_ready": True
            })
        
        return {
            "agents": playground_agents,
            "total": len(playground_agents),
            "limit": limit,
            "skip": skip,
            "playground_info": {
                "endpoints": [
                    "/agents/{agent_id}/playground/chat",
                    "/agents/playground/chat"
                ],
                "features": [
                    "Real-time streaming",
                    "Conversation context",
                    "Agent testing",
                    "Response analysis"
                ]
            }
        }
    
    except Exception as e:
        logger.error(f"Error getting playground agents: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error occurred while getting playground agents"
        )

@router.get("/{agent_id}/playground/info")
@rate_limit(rate_limit=100, window=60)
async def get_agent_playground_info(
    agent_id: str,
    context: RunningContext = Depends(read_agent_permission)
):
    """
    Get detailed playground information for a specific agent.
    """
    try:
        agent = await agent_service.get_agent(
            agent_id=agent_id,
            requester=context.current_user
        )
        
        if not agent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Agent not found"
            )
        
        return {
            "agent": {
                "id": str(agent.id),
                "name": agent.name,
                "description": agent.description,
                "type": getattr(agent, 'type', 'general'),
                "status": "active" if agent.is_active else "inactive",
                "model": getattr(agent, 'model', None),
                "created_at": agent.created_at,
                "updated_at": agent.updated_at
            },
            "playground": {
                "enabled": agent.is_active,
                "endpoint": f"/agents/{agent_id}/playground/chat",
                "features": [
                    "Streaming responses",
                    "Conversation history",
                    "Real-time testing",
                    "Performance metrics"
                ],
                "usage_tips": [
                    "Start with simple queries to test basic functionality",
                    "Use streaming mode for real-time feedback",
                    "Try complex multi-turn conversations",
                    "Test different query types and formats"
                ]
            }
        }
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting agent playground info: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error occurred while getting agent playground info"
        )