from typing import Dict, List, Optional, Any
from uuid import UUID, uuid4
from datetime import datetime, timezone
from fastapi import HTT<PERSON>Exception
from models.user import User
from .base import BaseRepository, ModelType

class MockedDB:
    """Mock database for testing purposes"""
    _collections: Dict[str, Dict[UUID, Dict[str, Any]]] = {}

    @classmethod
    def get_collection(cls, name: str) -> Dict[UUID, Dict[str, Any]]:
        if name not in cls._collections:
            cls._collections[name] = {}
        return cls._collections[name]

    @classmethod
    def clear_all(cls):
        """Clear all collections - useful for test cleanup"""
        cls._collections.clear()

class MockedDBRepository(BaseRepository[ModelType]):
    """In-memory implementation of the BaseRepository interface for testing purposes"""
    
    def __init__(self, collection_name: str, model_type: type):
        self.collection_name = collection_name
        self.model_type = model_type
        self._store = MockedDB.get_collection(collection_name)

    def _should_apply_tenant_filter(self) -> bool:
        context = self.context
        if not context or not context.tenant_id:
            return False
        
        # Check if user has system-level permissions instead of is_superuser
        # Note: This is a synchronous method, so we can't await async permission checks
        # For safety, we'll apply tenant filtering unless we can determine system access
        # through other means (like checking user role directly)
        try:
            # Check if user has system admin role directly (synchronous check)
            if hasattr(context.user, 'role_name') and context.user.role_name == 'SystemAdmin':
                return False
        except Exception:
            # If permission check fails, apply tenant filter for safety
            pass
            
        return self.model.__name__ not in ['User', 'Tenant', 'Role', 'APIKey']

    def _get_tenant_query_filter(self) -> Dict[str, Any]:
        if not self._should_apply_tenant_filter():
            return {}
        return {"tenant_id": self.required_context.tenant_id}

    async def create(self, data: ModelType, tenant_id_for_creation: Optional[str] = None) -> ModelType:
        doc_data = data.model_dump()
        doc_id = uuid4()
        doc_data["_id"] = doc_id

        # Add audit fields
        self._add_audit_fields(doc_data)
        
        # Handle tenant ID
        if tenant_id_for_creation and self.context:
            # Check if user has system-level permissions to set tenant_id
            try:
                system_permissions = ["super_admin", "system:create_tenant", "system:delete_tenant", 
                                    "system:list_tenants", "system:manage_users", "system:view_analytics"]
                has_system_access = any(self.context.has_permission(perm) for perm in system_permissions)
                if has_system_access:
                    doc_data["tenant_id"] = tenant_id_for_creation
            except Exception:
                # If permission check fails, don't allow tenant_id override
                pass
        elif self._should_apply_tenant_filter():
            doc_data["tenant_id"] = self.required_context.tenant_id
            
        # Add metadata
        self._add_context_metadata(doc_data)

        self._store[doc_id] = doc_data
        return self.model_type(**doc_data)

    async def get_by_id(self, doc_id: UUID) -> Optional[ModelType]:
        document = self._store.get(doc_id)
        if not document:
            return None
            
        # Check tenant scope
        tenant_filter = self._get_tenant_query_filter()
        for key, value in tenant_filter.items():
            if document.get(key) != value:
                return None
                
        return self.model_type(**document)

    async def get_all(self, skip: int = 0, limit: int = 100, 
                     query_conditions: Optional[Dict[str, Any]] = None) -> List[ModelType]:
        # Start with tenant filter
        query_filter = self._get_tenant_query_filter()
        
        # Add any additional conditions
        if query_conditions:
            query_filter.update(query_conditions)

        matching_docs = []
        for doc in self._store.values():
            matches = True
            for key, value in query_filter.items():
                if doc.get(key) != value:
                    matches = False
                    break
            if matches:
                matching_docs.append(doc)

        # Apply pagination
        paginated_docs = matching_docs[skip:skip + limit]
        return [self.model_type(**doc) for doc in paginated_docs]

    async def get_list_with_count(self, skip: int = 0, limit: int = 100,
                                 query_conditions: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Get paginated entities with total count for pagination metadata.
        """
        # Start with tenant filter
        query_filter = self._get_tenant_query_filter()

        # Add any additional conditions
        if query_conditions:
            query_filter.update(query_conditions)

        matching_docs = []
        for doc in self._store.values():
            matches = True
            for key, value in query_filter.items():
                if doc.get(key) != value:
                    matches = False
                    break
            if matches:
                matching_docs.append(doc)

        # Get total count
        total_count = len(matching_docs)

        # Apply pagination
        paginated_docs = matching_docs[skip:skip + limit]
        data = [self.model_type(**doc) for doc in paginated_docs]

        # Calculate pagination metadata
        has_more = (skip + len(data)) < total_count

        return {
            "data": data,
            "total_count": total_count,
            "skip": skip,
            "limit": limit,
            "has_more": has_more
        }

    async def update(self, doc_id: UUID, data: ModelType) -> Optional[ModelType]:
        # First check if document exists and is accessible
        if doc_id not in self._store:
            return None
            
        existing_doc = self._store[doc_id]
        
        # Check tenant scope
        tenant_filter = self._get_tenant_query_filter()
        for key, value in tenant_filter.items():
            if existing_doc.get(key) != value:
                return None

        # Prepare update data
        update_data = data.model_dump(exclude_unset=True)
        
        # Add audit fields
        self._add_audit_fields(update_data, is_update=True)
        
        # Protect certain fields
        for protected_field in ["created_at", "created_by", "id", "_id"]:
            update_data.pop(protected_field, None)
            
        # Add metadata
        self._add_context_metadata(update_data)
        
        # Handle tenant ID
        if "tenant_id" in update_data:
            if self.context:
                # Check if user has system-level permissions to modify tenant_id
                try:
                    system_permissions = ["super_admin", "system:create_tenant", "system:delete_tenant", 
                                        "system:list_tenants", "system:manage_users", "system:view_analytics"]
                    has_system_access = any(self.context.has_permission(perm) for perm in system_permissions)
                    if not has_system_access:
                        del update_data["tenant_id"]
                except Exception:
                    # If permission check fails, remove tenant_id for safety
                    del update_data["tenant_id"]
            else:
                # No context, remove tenant_id for safety
                del update_data["tenant_id"]

        # Update document
        self._store[doc_id].update(update_data)
        return self.model_type(**self._store[doc_id])

    async def delete(self, doc_id: UUID) -> bool:
        if doc_id not in self._store:
            return False
            
        document = self._store[doc_id]
        
        # Check tenant scope
        tenant_filter = self._get_tenant_query_filter()
        for key, value in tenant_filter.items():
            if document.get(key) != value:
                return False
                
        del self._store[doc_id]
        return True

    async def find_by_id_system(self, id: UUID) -> Optional[ModelType]:
        """System-level find that bypasses all context filtering"""
        document = self._store.get(id)
        return self.model_type(**document) if document else None

    async def find_all_system(self, filters: Dict[str, Any] = None, skip: int = 0, limit: int = 100) -> List[ModelType]:
        """System-level find all that bypasses all context filtering"""
        matching_docs = []
        for doc in self._store.values():
            if filters:
                matches = True
                for key, value in filters.items():
                    if doc.get(key) != value:
                        matches = False
                        break
                if matches:
                    matching_docs.append(doc)
            else:
                matching_docs.append(doc)

        # Apply pagination
        paginated_docs = matching_docs[skip:skip + limit]
        return [self.model_type(**doc) for doc in paginated_docs]