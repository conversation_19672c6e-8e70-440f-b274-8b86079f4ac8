from typing import <PERSON>V<PERSON>, Generic, List, Dict, Any, Optional, Set, Union, Protocol, AbstractSet, runtime_checkable
from models import BaseModel
from uuid import UUID, uuid4
from datetime import datetime
from abc import ABC, abstractmethod
from models.user import User
from session.running_context import Running<PERSON>ontext, get_context, require_context
from datetime import datetime, timezone

# Generic type for models
ModelType = TypeVar("ModelType", bound=BaseModel)

@runtime_checkable
class BaseRepository(Protocol[ModelType]):
    def __init__(self, collection_name: str, model_type: type):
        self.collection_name = collection_name
        self.model_type = model_type
    
    @property
    def context(self) -> Optional['RunningContext']:
        """Get current running context"""
        return get_context()
    
    @property
    def required_context(self) -> 'RunningContext':
        """Get running context - raises exception if no context"""
        return require_context()
    
    @property
    def user(self) -> Optional['User']:
        """Get current user from context"""
        context = self.context
        return context.user if context else None
    
    @property
    def tenant_id(self) -> Optional[str]:
        """Get current tenant ID from context"""
        context = self.context
        return context.tenant_id if context else None
    
    @property
    def user_id(self) -> Optional[str]:
        """Get current user ID from context"""
        context = self.context
        return context.user_id if context else None
    
    @property
    def request_id(self) -> Optional[str]:
        """Get current request ID from context"""
        context = self.context
        return context.request_id if context else None
    
    def _should_apply_tenant_filter(self) -> bool:
        """Determine if tenant filtering should be applied"""
        context = self.context
        if not context or not context.tenant_id:
            return False
        
        # Check if user has system-level permissions
        # Note: This is a synchronous method, so we can't use async permission checks
        # For now, we'll skip tenant filtering for system models
        return self.model_type.__name__ not in ['User', 'Tenant', 'Role', 'APIKey']
    
    def _add_tenant_filter(self, query: Dict[str, Any]) -> Dict[str, Any]:
        """Add tenant filtering to queries automatically"""
        if self._should_apply_tenant_filter():
            query = query.copy() if query else {}
            query['tenant_id'] = self.tenant_id
        return query or {}
    
    def _add_audit_fields(self, doc: Dict[str, Any], is_update: bool = False) -> Dict[str, Any]:
        """Add audit fields automatically"""
        context = self.context
        if not context:
            return doc
            
        # Add user audit fields
        if not is_update and 'created_by' not in doc:
            doc['created_by'] = context.user_id
        if is_update and 'updated_by' not in doc:
            doc['updated_by'] = context.user_id
            
        # Add timestamp
        timestamp_field = 'updated_at' if is_update else 'created_at'
        if timestamp_field not in doc:
            doc[timestamp_field] = datetime.now(timezone.utc)
        
    def _add_context_metadata(self, doc: Dict[str, Any]) -> Dict[str, Any]:
        """Add context metadata to document"""
        context = self.context
        if not context or not context.metadata:
            return doc
            
        # Add metadata prefixed with 'doc_' to the document
        for key, value in context.metadata.items():
            if key.startswith('doc_') and key[4:] not in doc:
                doc[key[4:]] = value
        return doc
    
    @abstractmethod
    async def create(self, data: ModelType, tenant_id_for_creation: Optional[str] = None) -> ModelType:
        """
        Create a new entity in the database.
        
        Args:
            data: The model instance to create
            tenant_id_for_creation: Optional tenant ID for superusers creating tenant-specific resources
            
        Returns:
            The created model instance
        """
        pass
        
    @abstractmethod
    async def get_by_id(self, doc_id: UUID) -> Optional[ModelType]:
        """
        Get an entity by its ID with tenant security checks.
        
        Args:
            doc_id: The UUID of the document to retrieve
            
        Returns:
            The model instance if found and accessible, None otherwise
        """
        pass
        
    @abstractmethod
    async def get_all(self, skip: int = 0, limit: int = 100,
                     query_conditions: Optional[Dict[str, Any]] = None) -> List[ModelType]:
        """
        Get all entities matching query conditions with tenant security.

        Args:
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return
            query_conditions: Optional additional query filters

        Returns:
            List of model instances matching the criteria
        """
        pass

    @abstractmethod
    async def get_list_with_count(self, skip: int = 0, limit: int = 100,
                                 query_conditions: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Get paginated entities with total count for pagination metadata.

        Args:
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return
            query_conditions: Optional additional query filters

        Returns:
            Dictionary containing:
            - "data": List[ModelType] - the paginated results
            - "total_count": int - total number of objects in the collection
            - "skip": int - number of records skipped
            - "limit": int - maximum records returned
            - "has_more": bool - whether there are more records available
        """
        pass
    
    @abstractmethod
    async def update(self, doc_id: UUID, data: ModelType) -> Optional[ModelType]:
        """
        Update an entity by ID with tenant security checks.
        
        Args:
            doc_id: The UUID of the document to update
            data: The model instance with updated data
            
        Returns:
            The updated model instance if successful, None if not found or not accessible
        """
        pass
        
    @abstractmethod
    async def delete(self, doc_id: UUID) -> bool:
        """
        Delete an entity by ID with tenant security checks.
        
        Args:
            doc_id: The UUID of the document to delete
            
        Returns:
            True if deleted successfully, False otherwise
        """
        pass

    # System-level operations (bypass context filtering)
    @abstractmethod
    async def find_by_id_system(self, id: UUID) -> Optional[ModelType]:
        """System-level find that bypasses all context filtering"""
        pass
    
    @abstractmethod
    async def find_all_system(self, skip: int = 0, limit: int = 100, filters: Dict[str, Any] = None) -> List[ModelType]:
        """System-level find all that bypasses all context filtering"""
        pass