import api from './api';

class ResourceService {
  
  // File Resources
  async uploadFile(formData) {
    try {
      const response = await api.post('/resources/files', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Article Resources
  async createArticle(articleData) {
    try {
      const formData = new FormData();
      Object.keys(articleData).forEach(key => {
        if (articleData[key] !== null && articleData[key] !== undefined) {
          if (Array.isArray(articleData[key])) {
            articleData[key].forEach(item => formData.append(key, item));
          } else {
            formData.append(key, articleData[key]);
          }
        }
      });

      const response = await api.post('/resources/articles', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Web Resources
  async createWebResource(webData) {
    try {
      const formData = new FormData();
      Object.keys(webData).forEach(key => {
        if (webData[key] !== null && webData[key] !== undefined) {
          if (Array.isArray(webData[key])) {
            webData[key].forEach(item => formData.append(key, item));
          } else {
            formData.append(key, webData[key]);
          }
        }
      });

      const response = await api.post('/resources/web', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // List Resources by Type
  async listResources(resourceType, options = {}) {
    try {
      const { skip = 0, limit = 10, ...filters } = options;
      const params = new URLSearchParams({
        skip: skip.toString(),
        limit: limit.toString(),
        ...filters
      });

      const response = await api.get(`/resources/${resourceType}?${params}`);
      // Normalize name for all resource types
      const resources = (response.data.resources || response.data.results || []).map(resource => ({
        ...resource,
        name:
          resourceType === 'file' ? resource.title :
          resourceType === 'article' ? (resource.title || resource.name) :
          resourceType === 'web' ? (resource.title || resource.name) :
          resource.name,
      }));
      return {
        ...response.data,
        resources,
      };
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Search Resources
  async searchResources(query, resourceType, options = {}) {
    try {
      const { k = 4, ...filters } = options;
      const params = new URLSearchParams({
        query,
        resource_type: resourceType,
        k: k.toString(),
        ...filters
      });

      const response = await api.get(`/resources/search?${params}`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Get Resource by ID and Type
  async getResource(resourceType, resourceId) {
    try {
      const response = await api.get(`/resources/${resourceType}/${resourceId}`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Convenience methods for specific resource types
  async getFileResource(resourceId) {
    return this.getResource('file', resourceId);
  }

  async getTextResource(resourceId) {
    return this.getResource('article', resourceId); // Note: backend uses 'article' for text resources
  }

  async getWebResource(resourceId) {
    return this.getResource('web', resourceId);
  }

  // Get Web Resource Children
  async getWebResourceChildren(resourceId, options = {}) {
    try {
      const { skip = 0, limit = 100 } = options;
      const params = new URLSearchParams({
        skip: skip.toString(),
        limit: limit.toString()
      });

      const response = await api.get(`/resources/web/${resourceId}/children?${params}`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Get Web Resource Hierarchy
  async getWebResourceHierarchy(resourceId) {
    try {
      const response = await api.get(`/resources/web/${resourceId}/hierarchy`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Get Sitemap Processing Details
  async getSitemapProcessingDetails(resourceId) {
    try {
      const response = await api.get(`/resources/web/${resourceId}/sitemap/details`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Update Resource
  async updateResource(resourceType, resourceId, updateData) {
    try {
      const response = await api.put(`/resources/${resourceType}/${resourceId}`, updateData);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Delete Resource
  async deleteResource(resourceType, resourceId) {
    try {
      const response = await api.delete(`/resources/${resourceType}/${resourceId}`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Bulk Delete Resources
  async deleteResources(resourceType, resourceIds) {
    try {
      const promises = resourceIds.map(id => this.deleteResource(resourceType, id));
      return await Promise.all(promises);
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Get Resource Statistics
  async getResourceStats(resourceType) {
    try {
      const response = await api.get(`/resources/stats/${resourceType}`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Download File Resource
  async downloadResource(resourceType, resourceId) {
    try {
      const response = await api.get(`/resources/${resourceType}/${resourceId}/download`, {
        responseType: 'blob',
      });
      
      // Create download link
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      
      // Try to get filename from headers
      const contentDisposition = response.headers['content-disposition'];
      let filename = 'download';
      if (contentDisposition) {
        const matches = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/.exec(contentDisposition);
        if (matches != null && matches[1]) {
          filename = matches[1].replace(/['"]/g, '');
        }
      }
      
      link.setAttribute('download', filename);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
      return { success: true, filename };
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Get All Resources (Combined from all types)
  async getAllResources(options = {}) {
    try {
      const { skip = 0, limit = 10, search = '', sortBy = 'created_at', sortOrder = 'desc' } = options;
      
      // Fetch from all resource types with full limit for each type
      const resourceTypes = ['file', 'article', 'web'];
      const promises = resourceTypes.map(type => 
        this.listResources(type, { skip: 0, limit: limit }) // Request full limit from each type
          .catch(() => ({ resources: [] })) // Handle individual failures gracefully
      );

      const results = await Promise.all(promises);
      
      // Combine and normalize results
      let allResources = [];
      results.forEach((result, index) => {
        const type = resourceTypes[index];
        // Handle both 'resources' and 'results' properties for backward compatibility
        const resourcesList = result.resources || result.results || [];
        if (resourcesList.length > 0) {
          const normalizedResources = resourcesList.map(resource => ({
            ...resource,
            type: type,
            // Ensure consistent field names
            name: resource.title || resource.name,
            created_at: resource.created_at,
            updated_at: resource.updated_at || resource.created_at,
            status: resource.status || 'processed',
            size: this.formatFileSize(resource.file_size) || '0 B',
            tags: resource.tags || [],
            description: resource.description || ''
          }));
          allResources = allResources.concat(normalizedResources);
        }
      });

      // Apply search filter
      if (search) {
        allResources = allResources.filter(resource =>
          resource.name?.toLowerCase().includes(search.toLowerCase()) ||
          resource.description?.toLowerCase().includes(search.toLowerCase()) ||
          resource.tags?.some(tag => tag.toLowerCase().includes(search.toLowerCase()))
        );
      }

      // Sort results
      allResources.sort((a, b) => {
        const aValue = a[sortBy] || '';
        const bValue = b[sortBy] || '';
        
        if (sortOrder === 'asc') {
          return aValue > bValue ? 1 : -1;
        } else {
          return aValue < bValue ? 1 : -1;
        }
      });

      // Apply pagination
      const paginatedResults = allResources.slice(skip, skip + limit);

      return {
        resources: paginatedResults,
        total: allResources.length,
        skip,
        limit
      };
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Utility Methods
  formatFileSize(bytes) {
    if (!bytes || bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  handleError(error) {
    if (error.response) {
      // Server responded with error status
      return {
        message: error.response.data?.detail || 'An error occurred',
        status: error.response.status,
        data: error.response.data
      };
    } else if (error.request) {
      // Request was made but no response received
      return {
        message: 'Network error - please check your connection',
        status: 0
      };
    } else {
      // Something else happened
      return {
        message: error.message || 'An unexpected error occurred',
        status: -1
      };
    }
  }

  // Create Resource Helper (routes to appropriate method based on type)
  async createResource(resourceData) {
    const { type, ...data } = resourceData;
    
    try {
      switch (type) {
        case 'file':
          const fileFormData = new FormData();
          fileFormData.append('title', data.name);
          if (data.description) fileFormData.append('description', data.description);
          if (data.file) fileFormData.append('file', data.file);
          if (data.tags && data.tags.length > 0) {
            data.tags.forEach(tag => fileFormData.append('tags', tag));
          }
          return await this.uploadFile(fileFormData);
          
        case 'article':
          return await this.createArticle({
            title: data.name,
            content: data.content,
            description: data.description,
            tags: data.tags
          });
          
        case 'web':
          return await this.createWebResource({
            title: data.name,
            url: data.content, // Backend expects 'url', frontend sends 'content' for websites
            description: data.description,
            tags: data.tags,
            load_type: data.websiteType || 'single', // Use the selected website type
            max_depth: data.websiteType === 'recursive' ? 2 : undefined,
            sitemap_url: data.websiteType === 'sitemap' ? data.content : undefined
          });
          
        default:
          throw new Error(`Unsupported resource type: ${type}`);
      }
    } catch (error) {
      // Re-throw with better error message formatting
      const formattedError = this.handleError(error);
      throw new Error(formattedError.message);
    }
  }

  // Reindex all resources of a specific type
  async reindexResources(resourceType, forceReindex = false) {
    try {
      const formData = new FormData();
      formData.append('force_reindex', forceReindex);

      const response = await api.post(`/resources/${resourceType}/reindex`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Reindex a single resource
  async reindexSingleResource(resourceType, resourceId) {
    try {
      const response = await api.post(`/resources/${resourceType}/${resourceId}/reindex`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Reindex all resources (all types)
  async reindexAllResources(forceReindex = false) {
    try {
      const resourceTypes = ['file', 'article', 'web'];
      const promises = resourceTypes.map(type => 
        this.reindexResources(type, forceReindex)
          .catch(error => ({ 
            resource_type: type, 
            error: error.message || 'Reindex failed',
            status: 'failed'
          }))
      );

      const results = await Promise.all(promises);
      
      return {
        status: 'completed',
        results: results,
        total_types: resourceTypes.length,
        successful: results.filter(r => r.status !== 'failed').length,
        failed: results.filter(r => r.status === 'failed').length
      };
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Reindex selected resources in bulk (more efficient than individual calls)
  async reindexSelectedResources(resources) {
    try {
      // Format resources as "type:id" pairs
      const resourceIds = resources.map(resource => `${resource.type}:${resource.id}`).join(',');
      
      const formData = new FormData();
      formData.append('resource_ids', resourceIds);

      const response = await api.post('/resources/bulk/reindex', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Fetch webpage title for auto-filling resource name (client-side)
  async fetchWebpageTitle(url) {
    try {
      // Validate URL format first
      const urlObj = new URL(url);
      if (!['http:', 'https:'].includes(urlObj.protocol)) {
        throw new Error('URL must use HTTP or HTTPS protocol');
      }

      // Try to fetch directly first (will work if CORS is enabled)
      try {
        const response = await fetch(url, {
          method: 'GET',
          mode: 'cors',
          headers: {
            'Accept': 'text/html,application/xhtml+xml',
          },
        });

        if (response.ok) {
          const html = await response.text();
          const title = this.extractTitleFromHTML(html);
          return { title, error: null };
        }
      } catch (corsError) {
        // CORS blocked, try alternative methods
        console.log('Direct fetch blocked by CORS, trying alternative methods...');
      }

      // Fallback: Use a CORS proxy service
      const proxyUrl = `https://api.allorigins.win/get?url=${encodeURIComponent(url)}`;
      const response = await fetch(proxyUrl);
      
      if (response.ok) {
        const data = await response.json();
        const title = this.extractTitleFromHTML(data.contents);
        return { title, error: null };
      }

      return { title: null, error: 'Failed to fetch webpage content' };
    } catch (error) {
      // Don't throw error for title fetching - just return null
      console.warn('Failed to fetch webpage title:', error);
      return { title: null, error: error.message };
    }
  }

  // Extract title from HTML content
  extractTitleFromHTML(html) {
    try {
      // Create a temporary DOM element to parse HTML
      const parser = new DOMParser();
      const doc = parser.parseFromString(html, 'text/html');
      
      // Try different methods to get the title
      let title = null;

      // 1. Try <title> tag
      const titleTag = doc.querySelector('title');
      if (titleTag && titleTag.textContent.trim()) {
        title = titleTag.textContent.trim();
      }

      // 2. Try Open Graph title
      if (!title) {
        const ogTitle = doc.querySelector('meta[property="og:title"]');
        if (ogTitle && ogTitle.getAttribute('content')) {
          title = ogTitle.getAttribute('content').trim();
        }
      }

      // 3. Try Twitter title
      if (!title) {
        const twitterTitle = doc.querySelector('meta[name="twitter:title"]');
        if (twitterTitle && twitterTitle.getAttribute('content')) {
          title = twitterTitle.getAttribute('content').trim();
        }
      }

      // 4. Try h1 tag as fallback
      if (!title) {
        const h1Tag = doc.querySelector('h1');
        if (h1Tag && h1Tag.textContent.trim()) {
          title = h1Tag.textContent.trim();
        }
      }

      // Clean up the title
      if (title) {
        // Remove excessive whitespace and normalize
        title = title.replace(/\s+/g, ' ').trim();
        // Limit length to reasonable size
        if (title.length > 100) {
          title = title.substring(0, 97) + '...';
        }
      }

      return title || null;
    } catch (error) {
      console.warn('Error extracting title from HTML:', error);
      return null;
    }
  }
}

export default new ResourceService();
