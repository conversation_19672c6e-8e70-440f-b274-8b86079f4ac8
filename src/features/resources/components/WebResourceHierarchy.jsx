import React, { useState, useEffect } from 'react';
import { 
  TreePine, 
  Globe, 
  ChevronRight, 
  ChevronDown, 
  ExternalLink, 
  Users, 
  Clock,
  AlertCircle,
  Loader2
} from 'lucide-react';
import { <PERSON><PERSON>, Badge, Card, CardContent, CardHeader, CardTitle } from 'components/ui';
import { resourceService } from 'services';

const WebResourceHierarchy = ({ resourceId, className }) => {
  const [hierarchyData, setHierarchyData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [expandedNodes, setExpandedNodes] = useState(new Set());

  useEffect(() => {
    if (resourceId) {
      fetchHierarchyData();
    }
  }, [resourceId]);

  const fetchHierarchyData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await resourceService.getWebResourceHierarchy(resourceId);
      setHierarchyData(response);
      
      // Auto-expand the current resource and its parents
      const newExpanded = new Set();
      if (response.hierarchy) {
        response.hierarchy.forEach(item => {
          if (item.id === resourceId || item.is_root) {
            newExpanded.add(item.id);
          }
        });
      }
      setExpandedNodes(newExpanded);
    } catch (err) {
      console.error('Failed to fetch hierarchy data:', err);
      setError(err.message || 'Failed to load hierarchy data');
    } finally {
      setLoading(false);
    }
  };

  const toggleNode = (nodeId) => {
    const newExpanded = new Set(expandedNodes);
    if (newExpanded.has(nodeId)) {
      newExpanded.delete(nodeId);
    } else {
      newExpanded.add(nodeId);
    }
    setExpandedNodes(newExpanded);
  };

  const renderHierarchyNode = (node, level = 0, isLast = false) => {
    const isExpanded = expandedNodes.has(node.id);
    const hasChildren = node.children && node.children.length > 0;
    const isCurrent = node.id === resourceId;

    return (
      <div key={node.id} className="relative">
        {/* Connection lines */}
        {level > 0 && (
          <div className="absolute left-0 top-0 bottom-0 w-6 flex items-center">
            <div className={`w-full h-px bg-gray-300 ${isLast ? 'mb-auto mt-6' : ''}`} />
            {!isLast && <div className="absolute left-0 top-6 bottom-0 w-px bg-gray-300" />}
          </div>
        )}

        <div 
          className={`flex items-center space-x-2 p-2 rounded-lg transition-colors ${
            isCurrent ? 'bg-blue-50 border border-blue-200' : 'hover:bg-gray-50'
          }`}
          style={{ marginLeft: `${level * 24}px` }}
        >
          {/* Expand/Collapse Button */}
          {hasChildren ? (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => toggleNode(node.id)}
              className="p-1 h-auto w-auto"
            >
              {isExpanded ? (
                <ChevronDown size={14} className="text-gray-500" />
              ) : (
                <ChevronRight size={14} className="text-gray-500" />
              )}
            </Button>
          ) : (
            <div className="w-6 h-6" /> // Spacer for alignment
          )}

          {/* Resource Icon */}
          <div className="flex-shrink-0">
            {node.is_root ? (
              <TreePine size={16} className="text-blue-600" />
            ) : (
              <Globe size={16} className="text-gray-500" />
            )}
          </div>

          {/* Resource Info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2">
              <p className={`text-sm font-medium truncate ${
                isCurrent ? 'text-blue-900' : 'text-gray-900'
              }`}>
                {node.title || node.name}
              </p>
              {node.is_root && (
                <Badge variant="soft" color="blue" className="text-xs">
                  Root
                </Badge>
              )}
              {node.child_count > 0 && (
                <Badge variant="soft" color="green" className="text-xs">
                  <Users size={10} className="mr-1" />
                  {node.child_count}
                </Badge>
              )}
            </div>
            {node.url && (
              <p className="text-xs text-gray-600 truncate">{node.url}</p>
            )}
            {node.crawl_strategy && node.crawl_strategy !== 'single' && (
              <div className="flex items-center space-x-2 mt-1">
                <Badge variant="outline" className="text-xs">
                  {node.crawl_strategy === 'recursive' ? 'Recursive' : 
                   node.crawl_strategy === 'sitemap' ? 'Sitemap' : 
                   node.crawl_strategy}
                </Badge>
                {node.last_crawled && (
                  <span className="text-xs text-gray-500 flex items-center">
                    <Clock size={10} className="mr-1" />
                    {new Date(node.last_crawled).toLocaleDateString()}
                  </span>
                )}
              </div>
            )}
          </div>

          {/* Actions */}
          <div className="flex items-center space-x-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => window.open(`/resources/web/${node.id}`, '_blank')}
              className="p-1 h-auto w-auto opacity-0 group-hover:opacity-100 transition-opacity"
            >
              <ExternalLink size={12} />
            </Button>
          </div>
        </div>

        {/* Children */}
        {hasChildren && isExpanded && (
          <div className="mt-1">
            {node.children.map((child, index) => 
              renderHierarchyNode(
                child, 
                level + 1, 
                index === node.children.length - 1
              )
            )}
          </div>
        )}
      </div>
    );
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <TreePine size={18} />
            <span>Resource Hierarchy</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 size={24} className="animate-spin text-gray-400" />
            <span className="ml-2 text-gray-600">Loading hierarchy...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <TreePine size={18} />
            <span>Resource Hierarchy</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8 text-red-600">
            <AlertCircle size={20} className="mr-2" />
            <span>{error}</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!hierarchyData || !hierarchyData.hierarchy || hierarchyData.hierarchy.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <TreePine size={18} />
            <span>Resource Hierarchy</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-gray-500">
            <TreePine size={32} className="mx-auto mb-2 opacity-50" />
            <p>No hierarchy data available</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <TreePine size={18} />
          <span>Resource Hierarchy</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-1 max-h-96 overflow-y-auto">
          {hierarchyData.hierarchy.map((node, index) => 
            renderHierarchyNode(
              node, 
              0, 
              index === hierarchyData.hierarchy.length - 1
            )
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default WebResourceHierarchy;
