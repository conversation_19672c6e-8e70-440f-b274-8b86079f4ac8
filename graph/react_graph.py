"""
ReAct Agent Graph for Playground
Using LangGraph's built-in ReAct agent implementation
"""
from typing import Dict, Any, List
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_openai import ChatOpenAI
from langchain_core.tools import tool
from langchain_core.tools.retriever import create_retriever_tool
from langchain_weaviate import WeaviateVectorStore
from langchain_google_genai import GoogleGenerativeAIEmbeddings
from langgraph.prebuilt import create_react_agent
from config import settings
import datetime
import random
import weaviate
import logging


# Define simple tools for the playground
@tool
def get_current_time() -> str:
    """Get the current date and time."""
    return datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")


@tool
def calculate(expression: str) -> str:
    """
    Safely calculate a mathematical expression.
    
    Args:
        expression: A mathematical expression like "2 + 2" or "10 * 3"
    """
    try:
        # Only allow safe mathematical operations
        allowed_chars = set('0123456789+-*/.() ')
        if not all(c in allowed_chars for c in expression):
            return "Error: Invalid characters in expression"
        
        result = eval(expression)
        return f"{expression} = {result}"
    except Exception as e:
        return f"Error calculating {expression}: {str(e)}"


@tool
def random_number(min_val: int = 1, max_val: int = 100) -> str:
    """
    Generate a random number between min_val and max_val.
    
    Args:
        min_val: Minimum value (default: 1)
        max_val: Maximum value (default: 100)
    """
    return f"Random number between {min_val} and {max_val}: {random.randint(min_val, max_val)}"


@tool
def word_count(text: str) -> str:
    """
    Count words, characters, and lines in the given text.
    
    Args:
        text: The text to analyze
    """
    words = len(text.split())
    chars = len(text)
    lines = len(text.split('\n'))
    return f"Text analysis: {words} words, {chars} characters, {lines} lines"


class KnowledgeRetrievalTool:
    """Wrapper class for knowledge retrieval tool with proper connection management"""

    def __init__(self, tenant_id: str = "00000000-0000-0000-0000-000000000001"):
        self.tenant_id = tenant_id
        self.weaviate_client = None
        self.retrieval_tool = None
        self._initialize()

    def _initialize(self):
        """Initialize the retrieval tool"""
        try:
            # Initialize embeddings
            embeddings = GoogleGenerativeAIEmbeddings(
                model="models/embedding-001",
                google_api_key=settings.google_ai_key
            )

            # Initialize Weaviate client
            # Check if we should use cloud or local Weaviate
            use_cloud = (hasattr(settings, 'weaviate_use_cloud') and
                        settings.weaviate_use_cloud and
                        settings.weaviate_api_key and
                        settings.weaviate_api_key != "your-weaviate-api-key")

            if use_cloud:
                logging.info("Connecting to Weaviate Cloud...")
                self.weaviate_client = weaviate.connect_to_weaviate_cloud(
                    cluster_url=settings.weaviate_cluster_url if hasattr(settings, 'weaviate_cluster_url') else settings.weaviate_url,
                    auth_credentials=weaviate.auth.AuthApiKey(settings.weaviate_api_key),
                )
            else:
                # Local Weaviate
                logging.info("Connecting to local Weaviate...")
                host = settings.weaviate_url.replace("http://", "").replace("https://", "")
                port = int(host.split(":")[-1]) if ":" in host else 8080
                host = host.split(":")[0] if ":" in host else host

                self.weaviate_client = weaviate.connect_to_local(
                    host=host,
                    port=port,
                )

            # Create WeaviateVectorStore
            # Use the collection name from model registry
            from models.registry import get_collection_name
            from models.resource import Chunk
            collection_name = get_collection_name(Chunk)

            vector_store = WeaviateVectorStore(
                client=self.weaviate_client,
                index_name=collection_name,  # Use the correct collection name from registry
                text_key="text",
                embedding=embeddings,
                use_multi_tenancy=True
            )

            # Create retriever with tenant filtering
            retriever = vector_store.as_retriever(
                search_type="similarity",
                search_kwargs={
                    "k": 5,  # Return top 5 most relevant document
                }
            )

            # Create the retrieval tool
            self.retrieval_tool = create_retriever_tool(
                retriever=retriever,
                name="knowledge_search",
                description="Search the knowledge base for relevant information. Use to anwer any questions about information"
            )

        except Exception as e:
            logging.error(f"Could not create knowledge retrieval tool: {e}")
            logging.error(f"Weaviate config - use_cloud: {getattr(settings, 'weaviate_use_cloud', 'not set')}, "
                         f"api_key: {'set' if getattr(settings, 'weaviate_api_key', None) else 'not set'}, "
                         f"url: {getattr(settings, 'weaviate_url', 'not set')}")
            self.retrieval_tool = None

    def get_tool(self):
        """Get the retrieval tool"""
        return self.retrieval_tool

    def close(self):
        """Close the Weaviate connection"""
        if self.weaviate_client:
            try:
                self.weaviate_client.close()
            except Exception as e:
                logging.warning(f"Error closing Weaviate client: {e}")


def create_knowledge_retrieval_tool(tenant_id: str = "00000000-0000-0000-0000-000000000001"):
    """
    searching knowledge base using WeaviateVectorStore

    Args:
        tenant_id: The tenant ID to use for multi-tenant search (defaults to test tenant)
    """
    tool_wrapper = KnowledgeRetrievalTool(tenant_id)
    return tool_wrapper.get_tool()


def create_react_playground_graph(tenant_id: str = "00000000-0000-0000-0000-000000000001"):
    """Create a ReAct agent using LangGraph's built-in implementation with knowledge retrieval"""

    # Initialize the LLM
    llm = ChatGoogleGenerativeAI(
        google_api_key=settings.google_ai_key,
        model="gemini-2.0-flash",
        temperature=0,
        max_tokens=None,
        timeout=None,
        max_retries=2,
        # other params...
    )

    # Create basic tools
    basic_tools = [get_current_time, calculate, random_number, word_count]

    # Create knowledge retrieval tool
    knowledge_tool = create_knowledge_retrieval_tool(tenant_id)

    # Combine all tools
    all_tools = basic_tools.copy()
    if knowledge_tool:
        all_tools.append(knowledge_tool)

    # System message for the ReAct agent
    system_message = """You are a helpful AI assistant with access to tools.

Available tools:
- get_current_time: Get the current date and time
- calculate: Perform mathematical calculations
- random_number: Generate random numbers
- word_count: Analyze text (count words, characters, lines)
- knowledge_search: Search the knowledge base for relevant information
Try answer the question if you can, if not use the knowledge_search tool to find the answer.
You can use these tools to help answer questions and solve problems. When users ask about specific information that might be in documents, use the knowledge_search tool to find relevant content. Always be helpful, accurate, and explain your reasoning when using tools."""

    # Create the ReAct agent using LangGraph's built-in implementation
    react_agent = create_react_agent(
        model=llm,
        tools=all_tools,
        state_modifier=system_message
    )
    return react_agent


# Create the ReAct agent graph with default test tenant
react_playground_graph = create_react_playground_graph()


def get_playground_graph_for_tenant(tenant_id: str):
    """
    Get a playground graph configured for a specific tenant

    Args:
        tenant_id: The tenant ID to configure the knowledge retrieval for

    Returns:
        A ReAct agent graph with tenant-specific knowledge access
    """
    return create_react_playground_graph(tenant_id)
