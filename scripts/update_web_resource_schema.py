#!/usr/bin/env python3
"""
WebResource Schema Update Script

This script updates the Weaviate schema for WebResource to include
the new parent-child relationship fields and enhanced status tracking.

Usage:
    python scripts/update_web_resource_schema.py
"""

import asyncio
import sys
import os
import logging
from pathlib import Path

# Add project root to Python path
root_dir = Path(__file__).parent.parent
sys.path.append(str(root_dir))

from data_access.weaviatedb import connect_to_weaviate, close_weaviate_connection, upsert_schemas
from models.resource import WebResource

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def main():
    """Update WebResource schema with new parent-child relationship fields"""
    try:
        print("🔄 Updating WebResource schema...")
        print("Connecting to Weaviate...")
        await connect_to_weaviate()
        
        print("Updating schemas to include new WebResource fields...")
        await upsert_schemas()
        
        print("✅ WebResource schema updated successfully!")
        print("\nNew fields added:")
        print("- parent_url: URL of the parent page")
        print("- root_url: URL of the root page")
        print("- is_root: Flag for original crawled resource")
        print("- pages_discovered: Total pages discovered during crawl")
        print("- processing_start_time: When processing started")
        print("- processing_end_time: When processing ended")
        print("- progress_percentage: Processing progress (0-100)")
        print("- status_message: Current status message")
        print("- estimated_completion_time: Estimated completion time")
        print("- page_title: Title extracted from the page")
        print("- content_length: Length of the content")
        print("- discovered_at: When this page was discovered")
        print("- sitemap_url: Original sitemap URL")
        print("- is_sitemap_child: Flag for sitemap-discovered pages")
        print("- crawl_order: Order in which page was discovered")
        print("- sitemap_depth: Depth in sitemap hierarchy")
        print("- parent_sitemap_url: URL of parent sitemap")
        print("- lastmod: Last modification date from sitemap")
        print("- changefreq: Change frequency from sitemap")
        print("- priority: Priority from sitemap")
        print("- sitemap_lastmod: Last modification of sitemap")
        print("- retry_count: Number of retry attempts")
        print("- last_error: Last error message")
        print("- error_details: Detailed error information")
        
    except Exception as e:
        logger.error(f"Error updating schema: {str(e)}")
        print(f"❌ Error: {str(e)}")
        sys.exit(1)
    finally:
        await close_weaviate_connection()

if __name__ == "__main__":
    asyncio.run(main())
